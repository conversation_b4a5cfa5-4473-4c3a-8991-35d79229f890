import * as z from 'zod';

export const profileSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

export const passwordSchema = z.object({
  currentPassword: z.string().min(8, { message: 'Password must be at least 8 characters' }),
  newPassword: z.string().min(8, { message: 'Password must be at least 8 characters' }),
  confirmPassword: z.string().min(8, { message: 'Password must be at least 8 characters' }),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const preferencesSchema = z.object({
  timeInterval: z.enum(['15', '30', '60'], {
    required_error: 'You need to select a time interval',
  }),
  startHour: z.string().regex(/^([01]?[0-9]|2[0-3])$/, {
    message: "Start hour must be between 0-23",
  }),
  endHour: z.string().regex(/^([01]?[0-9]|2[0-3])$/, {
    message: "End hour must be between 0-23",
  }),
  timeFormat: z.enum(['12', '24'], {
    required_error: 'You need to select a time format',
  }),
  darkMode: z.boolean().default(false),
  syncEnabled: z.boolean().default(true), // Enable sync by default
});

export type ProfileFormValues = z.infer<typeof profileSchema>;
export type PasswordFormValues = z.infer<typeof passwordSchema>;
export type PreferencesFormValues = z.infer<typeof preferencesSchema>;
