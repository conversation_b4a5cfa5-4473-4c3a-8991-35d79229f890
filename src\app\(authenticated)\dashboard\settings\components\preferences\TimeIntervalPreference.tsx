"use client"

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { UseFormReturn } from 'react-hook-form';
import { PreferencesFormValues } from '../../schemas';

interface TimeIntervalPreferenceProps {
  form: UseFormReturn<PreferencesFormValues>;
}

export function TimeIntervalPreference({ form }: TimeIntervalPreferenceProps) {
  return (
    <div className="bg-muted/10 p-3 rounded-md">
      <FormField
        control={form.control}
        name="timeInterval"
        render={({ field }) => (
          <FormItem className="mb-0">
            <FormLabel className="text-sm font-medium">Default Time Interval</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-wrap gap-2 mt-1"
              >
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="15" id="15-min" />
                  <FormLabel htmlFor="15-min" className="font-normal cursor-pointer text-sm">15 minutes</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="30" id="30-min" />
                  <FormLabel htmlFor="30-min" className="font-normal cursor-pointer text-sm">30 minutes</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="60" id="60-min" />
                  <FormLabel htmlFor="60-min" className="font-normal cursor-pointer text-sm">1 hour</FormLabel>
                </div>
              </RadioGroup>
            </FormControl>
            <FormDescription className="mt-1 text-xs">
              Set the default time interval for new time blocks
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
