"use client"

import { useState, useEffect } from 'react';
import { TimeBlock } from '@/lib/types';
import { ScrollArea } from '@/components/ui/scroll-area';
import { usePreferences } from '@/hooks/use-preferences';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { TimeBlockDetails } from './TimeBlockDetails';
import { TimeBlockCard } from './TimeBlockCard'; // Added import for TimeBlockCard

interface TimeBlockGridProps {
  date: Date;
  timeBlocks: TimeBlock[] | undefined; // Allow undefined for initial loading states
  onAddBlock: (startTime?: string) => void; // Optional: allow passing suggested start time
  onEditBlock: (timeBlock: TimeBlock) => void;
  onDeleteBlock?: (timeBlock: TimeBlock) => void;
  onUpdate: (id: string, data: Partial<TimeBlock>) => void;
}

const hours = Array.from({ length: 24 }, (_, i) => i);

export function TimeBlockGrid({
  date,
  timeBlocks,
  onAddBlock,
  onEditBlock,
  onDeleteBlock,
  onUpdate,
}: TimeBlockGridProps) {
  const { formatTime } = usePreferences();
  const [selectedBlock, setSelectedBlock] = useState<TimeBlock | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [currentHourIndex, setCurrentHourIndex] = useState<number>(-1);

  useEffect(() => {
    const updateCurrentHourMarker = () => {
      const now = new Date();
      const gridDate = new Date(date);

      if (
        now.getFullYear() !== gridDate.getFullYear() ||
        now.getMonth() !== gridDate.getMonth() ||
        now.getDate() !== gridDate.getDate()
      ) {
        setCurrentHourIndex(-1); // Not today, no marker
        return;
      }
      setCurrentHourIndex(now.getHours());
    };

    updateCurrentHourMarker();
    const timer = setInterval(updateCurrentHourMarker, 60000); // Update every minute
    return () => clearInterval(timer);
  }, [date]);

  const getBlocksForHour = (hour: number): TimeBlock[] => {
    if (!timeBlocks) return [];
    
    // Create a Map to track unique blocks by a composite key to prevent duplicates
    const uniqueBlocks = new Map<string, TimeBlock>();
    
    // Format the selected date for comparison
    const selectedDateStr = format(date, 'yyyy-MM-dd');
    
    // Filter blocks that belong to this hour and have time information
    timeBlocks.filter((block) => {
      // Skip blocks without time information
      if (!block.startTime || !block.endTime) return false;
      
      // Check if the block is for the selected date
      const blockDateStr = block.date;
      const isForSelectedDate = blockDateStr === selectedDateStr;
      
      // Only show blocks for the selected date
      if (!isForSelectedDate && !block.routineId) {
        return false;
      }
      
      // For routine tasks, ensure we only show the ones for the selected date
      if (block.routineId) {
        const blockDate = new Date(block.date);
        const selectedDate = new Date(date);
        
        // Only show routine tasks that match the selected date
        if (blockDate.getDate() !== selectedDate.getDate() || 
            blockDate.getMonth() !== selectedDate.getMonth() || 
            blockDate.getFullYear() !== selectedDate.getFullYear()) {
          return false;
        }
      }
      
      const [startHour, startMinute] = block.startTime.split(':').map(Number);
      const [endHour, endMinute] = block.endTime.split(':').map(Number);

      const blockStartTotalMinutes = startHour * 60 + startMinute;
      let blockEndTotalMinutes = endHour * 60 + endMinute;

      // Handle blocks ending at 00:00 on the next day as ending at 24:00 of current day for simplicity in daily view
      if (endHour === 0 && endMinute === 0 && blockStartTotalMinutes > 0 && blockEndTotalMinutes < blockStartTotalMinutes) {
        blockEndTotalMinutes = 24 * 60;
      }
      
      // If start time and end time are same, it might be an all-day event
      // Only show these in a specific hour (e.g., hour 0) to avoid duplication
      if (blockStartTotalMinutes === blockEndTotalMinutes && block.startTime === block.endTime) {
        if (hour !== 0) return false; // Only show in the first hour slot
      }

      // Check if the block intersects with the current hour
      const hourStartMinutes = hour * 60;
      const hourEndMinutes = (hour + 1) * 60;

      const isInHour = blockStartTotalMinutes < hourEndMinutes && blockEndTotalMinutes > hourStartMinutes;
      
      if (isInHour) {
        // Create a unique key for this block that includes all relevant properties
        // For routine tasks, include the routineId to ensure we don't show duplicates
        const uniqueKey = block.routineId 
          ? `${block.routineId}-${block.startTime}-${block.endTime}-${block.date}`
          : `${block.id}-${block.startTime}-${block.endTime}`;
        
        // Only add this block if we haven't seen it before with the same key
        if (!uniqueBlocks.has(uniqueKey)) {
          uniqueBlocks.set(uniqueKey, block);
          return true;
        }
      }
      
      return false;
    });
    
    // Return the unique blocks for this hour
    return Array.from(uniqueBlocks.values());
  };

  const handleAddBlock = (hour: number) => {
    const suggestedStartTime = `${hour.toString().padStart(2, '0')}:00`;
    onAddBlock(suggestedStartTime);
  };

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-grow h-[calc(100vh_-_100px)]"> {/* Adjust 100px based on actual header/nav height */}
        <div className="p-1 pr-2"> {/* Added pr-2 for scrollbar */}
          {hours.map((hour, index) => {
            const blocksForThisHour = getBlocksForHour(hour);
            return (
              <div
                key={hour}
                className={cn(
                  "grid grid-cols-[45px_1fr] sm:grid-cols-[50px_1fr] md:grid-cols-[55px_1fr] items-stretch",
                  index < hours.length -1 && "border-b" // Add border to all but the last item
                  )} >
                {/* Hour Label Cell */}
                <div
                  className={cn(
                    "sticky left-0 bg-background z-10 py-2 px-1 text-sm sm:text-xs md:text-sm flex items-center justify-end border-r select-none",
                    index === currentHourIndex ? "text-primary font-semibold" : "text-muted-foreground"
                  )}
                >
                  {formatTime(`${hour.toString().padStart(2, '0')}:00`)}
                </div>

                {/* Cards Container Cell */}
                <div className={cn(
                  "flex overflow-x-auto space-x-2 p-2 min-h-[136px] items-center scrollbar-thin scrollbar-thumb-rounded scrollbar-track-transparent",
                  index === currentHourIndex ? "bg-primary/5" : "",
                  "hover:scrollbar-thumb-muted-foreground/40 scrollbar-thumb-muted-foreground/20"
                  )}>
                  {blocksForThisHour.length === 0 ? (
                    <div
                      className="flex-1 h-full min-h-[120px] flex items-center justify-center text-xs text-muted-foreground rounded cursor-pointer hover:bg-muted/30 transition-colors"
                      onClick={() => handleAddBlock(hour)}
                    >
                      No events for this hour. Click to add.
                    </div>
                  ) : (
                    blocksForThisHour.map((block) => {
                      // Create a more comprehensive unique key to avoid duplicate blocks
                      const blockKey = block.routineId 
                        ? `${block.routineId}-${block.id}-${block.startTime}-${block.endTime}-${block.date}` 
                        : block.id;
                      
                      return (
                        <div key={blockKey} className="h-[128px] flex-shrink-0 transition-all hover:scale-[1.02]"> 
                          <TimeBlockCard
                            block={block}
                            onEdit={onEditBlock}
                            onUpdate={onUpdate}
                            onClick={() => {
                              setSelectedBlock(block);
                              setIsDetailsOpen(true);
                            }}
                          />
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </ScrollArea>

      <TimeBlockDetails
        isOpen={isDetailsOpen}
        onClose={() => setIsDetailsOpen(false)}
        timeBlock={selectedBlock}
        date={date}
        onEdit={onEditBlock}
        onDelete={onDeleteBlock}
      />
    </div>
  );
}
