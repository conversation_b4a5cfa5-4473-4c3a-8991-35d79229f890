"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { useProfile } from '@/hooks/use-profile';
import { usePreferences } from '@/hooks/use-preferences';
import { ProfileForm, PasswordForm, PreferencesForm } from './components';
import { PreferencesFormValues } from './schemas';

export default function SettingsPage() {
  // Use our improved hooks
  const { profile, isLoading, refreshProfile } = useProfile();
  const { preferences, savePreferences } = usePreferences();

  // Function to handle saving preferences
  const handleSavePreferences = async (values: PreferencesFormValues) => {
    return await savePreferences(values);
  };

  return (
    <div className="space-y-3 mx-2 md:mx-10">
      <Tabs defaultValue="profile" className="w-full ">
        <TabsList className="h-8 w-full mt-2">
          <TabsTrigger value="profile" className="text-md h-6 px-3">Profile</TabsTrigger>
          <TabsTrigger value="preferences" className="text-md h-6 px-3">Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="mt-3 space-y-3">
          {/* Profile Form Component */}
          <ProfileForm
            initialData={{
              name: profile?.name || '',
              email: profile?.email || '',
            }}
            onProfileUpdate={refreshProfile}
          />

          {/* Password Form Component */}
          <PasswordForm />
        </TabsContent>

        <TabsContent value="preferences" className="mt-3">
          {/* Preferences Form Component */}
          <PreferencesForm
            initialData={{
              timeInterval: preferences?.timeInterval || '60',
              startHour: preferences?.startHour || '0',
              endHour: preferences?.endHour || '23',
              timeFormat: preferences?.timeFormat || '24',
              darkMode: preferences?.darkMode || false,
              syncEnabled: preferences?.syncEnabled !== undefined ? preferences.syncEnabled : false,
              customTimeBlocks: preferences?.customTimeBlocks || [],
            }}
            onSavePreferences={handleSavePreferences}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}