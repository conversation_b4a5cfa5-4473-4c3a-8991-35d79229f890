if(!self.define){let e,t={};const s=(s,a)=>(s=new URL(s+".js",a).href,t[s]||new Promise((t=>{if("document"in self){const e=document.createElement("script");e.src=s,e.onload=t,document.head.appendChild(e)}else e=s,importScripts(s),t()})).then((()=>{let e=t[s];if(!e)throw new Error(`Module ${s} didn’t register its module`);return e})));self.define=(a,c)=>{const i=e||("document"in self?document.currentScript.src:"")||location.href;if(t[i])return;let n={};const o=e=>s(e,i),r={module:{uri:i},exports:n,require:o};t[i]=Promise.all(a.map((e=>r[e]||o(e)))).then((e=>(c(...e),n)))}}define(["./workbox-00a24876"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"c2de045f03b99953214191da9b125ec5"},{url:"/_next/static/SDDPT351bUxtogZNpRGca/_buildManifest.js",revision:"aedc2448c049f348fbfe7802f436919c"},{url:"/_next/static/SDDPT351bUxtogZNpRGca/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/1435-f674bc7cb0c0028c.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/1498-2a8a82ab453104f2.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/1684-e72fa286cacab94d.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/2919-d793f02e98c468ef.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/3217-693c473f27abdd86.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/472.a3826d29d6854395.js",revision:"a3826d29d6854395"},{url:"/_next/static/chunks/4bd1b696-88fa3b48542c92e2.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/5106-828b3a2aff408e68.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/5521-f09633c38776e315.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/5952-31664130b6b2fe94.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/6270-94f0dc8c884ee7d1.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/6671-15896d19a1609a7d.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/7084-c86349c234a952be.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/7157-065d112c2579c28d.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/720-98e3f10641e376ab.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/8676-afbbadc9da3c6d3f.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/9341.c6f133d39e4d01aa.js",revision:"c6f133d39e4d01aa"},{url:"/_next/static/chunks/9460-efc657bb4252d7d6.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/9713-ca08a566c992f916.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/(authenticated)/dashboard/page-f64dfc5f8637fc77.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/(authenticated)/dashboard/settings/page-11cc667e72e1e5ab.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/(authenticated)/home/<USER>",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/(authenticated)/layout-16456bd6063158bc.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/(authenticated)/routine/layout-10161249f2740835.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/(authenticated)/routine/page-1101fe4e0224177f.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/_not-found/page-26af69f944f184ff.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/auth/login/route-769354ecf7dbcf0d.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/auth/logout/route-5ac6d74332991848.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/auth/password/route-5fc16bbd643f1e3c.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/auth/profile/route-7823b89b347f8e67.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/auth/register/route-e428b6b3b32428c0.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/categories/%5Bid%5D/route-5b731b153c2bce18.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/categories/route-b89f68b2cbdb946f.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/routines/%5Bid%5D/route-237f85f2e40e88b7.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/routines/convert-to-todos/route-8080dace9fe706cb.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/routines/route-774b66fa9d6b95ad.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/timeblocks/%5Bid%5D/route-57506bd0f9ef869c.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/timeblocks/date/%5Bdate%5D/route-4ddac59714c8ac88.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/timeblocks/route-07dc543dd34b15d4.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/api/todos/route-a9016491914422f9.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/auth/login/page-7afdd74cf1293028.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/auth/register/page-b966aba66dbaa20a.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/layout-cb1417e286d725be.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/app/page-bd669357041ee7dc.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/framework-4b11fe422fbadda6.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/main-a4f22e6de2ad93bc.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/main-app-d80910b6841a3705.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/pages/_app-b695b39e5fe4f954.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/pages/_error-ac9b802df6534269.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-be3d8c49ddedd370.js",revision:"SDDPT351bUxtogZNpRGca"},{url:"/_next/static/css/693cb26322951968.css",revision:"693cb26322951968"},{url:"/_next/static/media/26a46d62cd723877-s.woff2",revision:"befd9c0fdfa3d8a645d5f95717ed6420"},{url:"/_next/static/media/55c55f0601d81cf3-s.woff2",revision:"43828e14271c77b87e3ed582dbff9f74"},{url:"/_next/static/media/581909926a08bbc8-s.woff2",revision:"f0b86e7c24f455280b8df606b89af891"},{url:"/_next/static/media/6d93bde91c0c2823-s.woff2",revision:"621a07228c8ccbfd647918f1021b4868"},{url:"/_next/static/media/97e0cb1ae144a2a9-s.woff2",revision:"e360c61c5bd8d90639fd4503c829c2dc"},{url:"/_next/static/media/a34f9d1faa5f3315-s.p.woff2",revision:"d4fe31e6a2aebc06b8d6e558c9141119"},{url:"/_next/static/media/df0a9ae256c0569c-s.woff2",revision:"d54db44de5ccb18886ece2fda72bdfe0"},{url:"/android-chrome-192x192.png",revision:"fb70b3018479493039d3a839b294a4bd"},{url:"/android-chrome-512x512.png",revision:"dbeb93109d5e491cc83462caa57f05d1"},{url:"/apple-touch-icon.png",revision:"37ef9e70628c5d28fe5712ab564d376e"},{url:"/favicon-16x16.png",revision:"5c6c4e0948ce0253c4253a095707c6f0"},{url:"/favicon-32x32.png",revision:"f61407649d3c390cb542d2d194eda8c5"},{url:"/favicon.ico",revision:"af3015ba915582774808af189235e8e9"},{url:"/manifest.json",revision:"74d376acbb6df07fc5e1184fe2704f44"},{url:"/offline.html",revision:"fb41cf6c53dd10495f06a601f06fd532"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:t,event:s,state:a})=>t&&"opaqueredirect"===t.type?new Response(t.body,{status:200,statusText:"OK",headers:t.headers}):t}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic|googleapis)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts",plugins:[new e.ExpirationPlugin({maxEntries:20,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.CacheFirst({cacheName:"images",plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:2592e3})]}),"GET"),e.registerRoute(/\.(?:js|css)$/i,new e.StaleWhileRevalidate({cacheName:"static-resources",plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/^https?:\/\/.*\/api\/.*$/i,new e.NetworkFirst({cacheName:"api-responses",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:3600})]}),"GET"),e.registerRoute(/.*/i,new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:86400})]}),"GET")}));
